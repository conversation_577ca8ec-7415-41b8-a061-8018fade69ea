# 权益卡购买问题修复

## 问题描述

用户购买权益卡后，权益卡购买订单记录中显示状态为已支付，但并没有给用户的账号发放权益卡。

## 问题分析

经过代码分析，发现了以下问题：

### 1. 支付回调解密未实现
- 在 `src/controller/openapi/pay-callback.controller.ts` 中，微信支付回调的解密逻辑没有正确实现
- `decryptedData` 对象中的 `out_trade_no` 是空字符串，导致无法正确识别订单
- 因此权益卡发放逻辑无法被触发

### 2. 权益卡发放逻辑缺失
- 在 `src/service/wepay.service.ts` 中处理"该订单已支付"错误时，只更新了订单状态
- 但没有调用权益卡发放逻辑，导致权益卡未被发放给用户

### 3. 状态同步不完整
- 权益卡订单状态同步方法 `syncMembershipCardOrderStatus` 只更新订单状态
- 没有检查和发放权益卡

## 修复方案

### 1. 修复 wepay.service.ts 中的权益卡发放逻辑

在处理"该订单已支付"错误时，添加权益卡发放逻辑：

```typescript
// 检查是否已经发放权益卡，如果没有则发放
const existingCard = await this.customerMembershipCardService.findOne({
  where: {
    customerId: order.customerId,
    cardTypeId: order.cardTypeId,
    purchaseTime: order.createdAt,
  }
});

if (!existingCard) {
  // 发放权益卡逻辑
  // ...
}
```

### 2. 完善状态同步方法

修改 `syncMembershipCardOrderStatus` 方法，在同步订单状态的同时检查和发放权益卡。

### 3. 添加管理端同步接口

在 `src/controller/admin/membership-card-order-admin.controller.ts` 中添加手动同步接口：

```typescript
@Post('/:id/sync-status', { summary: '管理端同步权益卡订单状态并发放权益卡' })
async syncOrderStatus(@Param('id') id: number, @Body('operatorId') operatorId: number)
```

## 修复内容

### 1. 修改文件：src/service/wepay.service.ts

- 添加必要的导入：`CustomerMembershipCardService`、`CustomerService`、`MembershipCardType`
- 在处理"该订单已支付"错误时添加权益卡发放逻辑
- 修改 `syncMembershipCardOrderStatus` 方法，添加权益卡发放检查

### 2. 修改文件：src/controller/admin/membership-card-order-admin.controller.ts

- 添加 `syncOrderStatus` 方法，用于管理端手动同步订单状态并发放权益卡
- 支持查询微信支付状态并同步本地订单状态
- 检查权益卡是否已发放，未发放则自动发放

## 使用方法

### 对于已经支付但未发放权益卡的订单

1. 使用管理端接口手动同步：
   ```
   POST /admin/membership-card-orders/{订单ID}/sync-status
   Body: { "operatorId": 管理员ID }
   ```

2. 或者使用微信支付状态查询接口：
   ```
   GET /admin/wepay/transactions/sn/{订单编号}
   ```

### 对于新的权益卡购买

修复后的代码会自动处理：
- 微信支付成功后的回调处理
- "该订单已支付"错误的处理
- 订单状态同步时的权益卡发放

## 权益卡发放逻辑

1. **检查是否已发放**：查询用户是否已有对应的权益卡
2. **获取权益卡类型信息**：查询权益卡类型的配置
3. **计算有效期**：根据权益卡类型的 `validDays` 计算到期时间
4. **设置剩余次数**：根据权益卡类型的 `usageLimit` 设置剩余使用次数
5. **创建权益卡记录**：调用 `customerMembershipCardService.createCard`
6. **更新会员状态**：将用户会员状态更新为权益会员

## 注意事项

1. **重复发放检查**：所有发放逻辑都会检查是否已经发放过权益卡，避免重复发放
2. **事务安全**：建议在生产环境中将订单状态更新和权益卡发放放在数据库事务中
3. **日志记录**：所有操作都有详细的日志记录，便于问题排查
4. **支付回调解密**：长期解决方案是实现正确的微信支付回调解密逻辑

## 测试建议

1. 测试已支付但未发放权益卡的订单同步
2. 测试新的权益卡购买流程
3. 测试重复同步不会重复发放权益卡
4. 测试不同类型权益卡（折扣卡、次卡）的发放
5. 测试有效期和次数限制的正确设置
