import { Body, Controller, Get, Inject, Post, Query } from '@midwayjs/core';
import { WeappService } from '../../service/weapp.service';
import { Customer, Employee } from '../../entity';
import { CustomError } from '../../error/custom.error';
import { JwtService } from '@midwayjs/jwt';

@Controller('/openapi/weapp')
export class WeappController {
  @Inject()
  jwtService: JwtService;

  @Inject()
  weappService: WeappService;

  @Post('/code2Session', {
    summary: '小程序登录',
    description:
      '登录凭证校验。通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。',
  })
  async getAccessToken(
    @Body()
    body: {
      code: string;
      logintype: 'customer' | 'employee';
    }
  ) {
    // TODO: 这里要做用户的自动注册和登录，生成token让客户端在后续业务接口中使用
    const { session_key, openid } = await this.weappService.code2Session(
      body.code
    );
    console.log(session_key, openid);
    let user: Customer | Employee;
    switch (body.logintype) {
      case 'customer':
        user = await Customer.findOne({
          where: {
            openid,
          },
        });
        break;
      case 'employee':
        user = await Employee.findOne({
          where: {
            openid,
          },
        });
        break;
      default:
        console.error('用户类型错误');
        throw new CustomError('用户类型错误');
    }
    if (!user) {
      console.error('用户未注册');
      // 用户未注册时返回 openid，用于后续的注册流程
      return {
        openid,
      };
    }

    // 检查客户状态（仅对customer类型用户）
    if (body.logintype === 'customer') {
      const customer = user as Customer;
      if (customer.status === 0) {
        throw new CustomError('账户已被禁用，请联系客服', 403);
      }
    }
    // 生成访问 token
    const accessPayload = {
      userId: user.id,
      type: 'access',
      userType: body.logintype, // 根据登录类型设置用户类型
    };
    const token = this.jwtService.signSync(accessPayload, { expiresIn: '7d' });

    // 生成刷新 token，用于刷新访问 token，30 天过期，暂时未使用
    const refreshPayload = {
      userId: user.id,
      type: 'refresh',
    };
    const refreshToken = this.jwtService.signSync(refreshPayload, {
      expiresIn: '30d',
    });

    return {
      token,
      refreshToken,
      user,
      openid,
    };
  }

  @Get('/getPhoneNumber', {
    summary: '获取用户手机号',
    description:
      '该接口用于将code换取用户手机号。 说明，每个code只能使用一次，code的有效期为5min。',
  })
  async getPhoneNumber(
    @Query('code') code: string,
    @Query('isMember') isMember: boolean
  ) {
    return this.weappService.getPhoneNumber(code, isMember);
  }
}
