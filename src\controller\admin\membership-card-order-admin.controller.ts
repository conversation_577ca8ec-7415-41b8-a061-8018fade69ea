import {
  Controller,
  Get,
  Post,
  Del,
  Inject,
  Param,
  Query,
  Body,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../../error/custom.error';
import { MembershipCardOrderService } from '../../service/membership-card-order.service';
import { CustomerMembershipCardService } from '../../service/customer-membership-card.service';
import { WepayService } from '../../service/wepay.service';
import {
  MembershipCardOrder,
  MembershipCardOrderStatus,
} from '../../entity/membership-card-order.entity';

@Controller('/admin/membership-card-orders')
export class MembershipCardOrderAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  membershipCardOrderService: MembershipCardOrderService;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  wepayService: WepayService;

  @Get('/', { summary: '管理端查询所有权益卡订单列表' })
  async findAll(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 20,
    @Query('status') status?: string,
    @Query('customerId') customerId?: number,
    @Query('cardTypeId') cardTypeId?: number,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    const query: any = {};

    if (status) {
      query.status = status.split(',');
    }

    if (customerId) {
      query.customerId = customerId;
    }

    if (cardTypeId) {
      query.cardTypeId = cardTypeId;
    }

    // 时间范围筛选
    if (startTime || endTime) {
      query.createdAt = {};
      if (startTime) {
        query.createdAt.$gte = new Date(startTime);
      }
      if (endTime) {
        query.createdAt.$lte = new Date(endTime);
      }
    }

    return await this.membershipCardOrderService.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: ['customer', 'cardType'],
    });
  }

  @Get('/:id', { summary: '管理端查询权益卡订单详情' })
  async findById(@Param('id') id: number) {
    const order = await this.membershipCardOrderService.findById(id);

    if (!order) {
      throw new CustomError('权益卡订单不存在', 404);
    }

    return order;
  }

  @Post('/:id/refund', { summary: '管理端处理权益卡订单退款（任何状态）' })
  async refundOrder(
    @Param('id') id: number,
    @Body('operatorId') operatorId: number,
    @Body('reason') reason?: string,
    @Body('refundAmount') refundAmount?: number
  ) {
    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const order = await MembershipCardOrder.findByPk(id, {
      include: ['customer', 'cardType'],
    });

    if (!order) {
      throw new CustomError('权益卡订单不存在');
    }

    // 管理员退款无需状态检查，可以对任何状态的订单操作
    this.ctx.logger.info('【管理端权益卡订单退款】：', {
      orderId: id,
      orderSn: order.sn,
      operatorId,
      reason,
      refundAmount: refundAmount || order.amount,
      currentStatus: order.status,
    });

    try {
      // 如果订单已支付，执行微信退款
      if (order.status === MembershipCardOrderStatus.PAID) {
        await this.wepayService.adminRefundMembershipCardOrder(
          order.sn,
          refundAmount || order.amount
        );
      }

      // 更新订单状态
      await order.update({
        status: MembershipCardOrderStatus.REFUNDED,
        refundTime: new Date(),
      });

      // 禁用相关的用户权益卡
      await this.customerMembershipCardService.disableCardsByOrder(
        order.customerId,
        order.cardTypeId,
        operatorId,
        `权益卡订单退款：${reason || '管理员操作'}`
      );

      this.ctx.logger.info('【管理端权益卡订单退款成功】：', {
        orderId: id,
        orderSn: order.sn,
        refundAmount: refundAmount || order.amount,
      });

      return {
        success: true,
        refundAmount: refundAmount || order.amount,
        refundStatus: 'completed',
        message: '权益卡订单退款成功',
        operator: {
          id: operatorId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('【管理端权益卡订单退款失败】：', {
        orderId: id,
        error: error.message,
      });
      throw new CustomError(`退款失败：${error.message}`);
    }
  }

  @Del('/:id', { summary: '管理端删除权益卡订单（任何状态）' })
  async deleteOrder(
    @Param('id') id: number,
    @Body('operatorId') operatorId: number,
    @Body('reason') reason?: string
  ) {
    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const order = await MembershipCardOrder.findByPk(id, {
      include: ['customer', 'cardType'],
    });

    if (!order) {
      throw new CustomError('权益卡订单不存在');
    }

    this.ctx.logger.info('【管理端删除权益卡订单】：', {
      orderId: id,
      orderSn: order.sn,
      operatorId,
      reason,
      currentStatus: order.status,
    });

    try {
      // 如果订单已支付，先执行退款
      if (order.status === MembershipCardOrderStatus.PAID) {
        await this.wepayService.adminRefundMembershipCardOrder(
          order.sn,
          order.amount
        );

        // 禁用相关的用户权益卡
        await this.customerMembershipCardService.disableCardsByOrder(
          order.customerId,
          order.cardTypeId,
          operatorId,
          `权益卡订单删除：${reason || '管理员操作'}`
        );
      }

      // 删除订单记录
      await order.destroy();

      this.ctx.logger.info('【管理端删除权益卡订单成功】：', {
        orderId: id,
        orderSn: order.sn,
      });

      return {
        success: true,
        message: '权益卡订单删除成功',
        operator: {
          id: operatorId,
        },
      };
    } catch (error) {
      this.ctx.logger.error('【管理端删除权益卡订单失败】：', {
        orderId: id,
        error: error.message,
      });
      throw new CustomError(`删除失败：${error.message}`);
    }
  }

  @Post('/:id/cancel', { summary: '管理端取消权益卡订单' })
  async cancelOrder(
    @Param('id') id: number,
    @Body('operatorId') operatorId: number,
    @Body('reason') reason?: string
  ) {
    if (!operatorId) {
      throw new CustomError('操作员ID不能为空');
    }

    const order = await MembershipCardOrder.findByPk(id);

    if (!order) {
      throw new CustomError('权益卡订单不存在');
    }

    // 管理员可以取消任何状态的订单（除了已退款的）
    if (order.status === MembershipCardOrderStatus.REFUNDED) {
      throw new CustomError('已退款的订单无法取消');
    }

    await order.update({
      status: MembershipCardOrderStatus.CANCELLED,
      cancelTime: new Date(),
    });

    this.ctx.logger.info('【管理端取消权益卡订单】：', {
      orderId: id,
      orderSn: order.sn,
      operatorId,
      reason,
    });

    return {
      success: true,
      message: '权益卡订单取消成功',
      operator: {
        id: operatorId,
      },
    };
  }

  @Get('/statistics', { summary: '管理端权益卡订单统计' })
  async getStatistics(
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    const query: any = {};

    if (startTime || endTime) {
      query.createdAt = {};
      if (startTime) {
        query.createdAt.$gte = new Date(startTime);
      }
      if (endTime) {
        query.createdAt.$lte = new Date(endTime);
      }
    }

    const statistics = await this.membershipCardOrderService.getStatistics(
      query
    );

    return statistics;
  }
}
